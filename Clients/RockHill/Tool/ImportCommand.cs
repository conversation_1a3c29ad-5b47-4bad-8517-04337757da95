using LiteDB;
using Payroll.Shared;
using Serilog;
using System.Text.Json;

namespace RockHill.Tool
{
    class RockhillIdGenerator
    {
        uint nextId = Config.NextIdStart();
        ILiteCollection<Employee> _empCache;

        public RockhillIdGenerator(ILiteCollection<Employee> empCache)
        {
            _empCache = empCache;
        }

        public uint GetNextAvailableId()
        {
            // Find the first available ID. Yes, this will get slower over time. Client specification.
            Employee findEmp;

            do
            {
                nextId++;
                var idString = nextId.ToString().PadLeft(10, '0');
                Log.Logger.Debug("Searching for {0}", idString);
                findEmp = _empCache.FindOne(x => x.ClockSeq == idString);
            } while (findEmp != null);

            return nextId;
        }
    }

    internal class ImportCommand
    {
        private static List<EmployeeDirectoryEntry> ValidNewHiresToDirectoryEntries(IEnumerable<Employee> employees)
        {
            return Converter.EmployeesToDirectoryEntries(employees, Converter.ValidNewHire, true);
        }

        public int Employees(List<string> args)
        {
            (int exitCode, int recordCount) = ProcessImportCommand(args, (employees) =>
            {
                var currentEmployees = new List<EmployeeDirectoryEntry>();

                foreach (var employee in employees)
                {
                    if (string.IsNullOrEmpty(employee.ClockSeq))
                    {
                        Log.Logger.Error("Skipping new employee record (id={0})", employee.Id);
                        continue;
                    }

                    if (string.IsNullOrEmpty(employee.LastName) ||
                        string.IsNullOrEmpty(employee.FirstName))
                    {
                        Log.Logger.Error("First or last name missing, skipping employee record (id={0})", employee.Id);
                        continue;
                    }

                    var dEntry = Converter.EmployeeToDirectoryEntry(employee, false);

                    Converter.AddPolicyAndGroupAttributesVersion2(dEntry);
                    currentEmployees.Add(dEntry);
                }

                ConsoleService.PrintFormattedJson(currentEmployees);
                return currentEmployees.Count;
            });


            return exitCode;
        }

        public int Badgefix(List<string> args)
        {
            (int exitCode, int recordCount) = ProcessImportCommand(args, (employees) =>
            {
                var currentEmployees = new List<Employee>();

                foreach (var employee in employees)
                {
                    var attrs = employee.Attributes;
                    if (attrs == null || string.IsNullOrEmpty(employee.ClockSeq))
                    {
                        Log.Logger.Warning("Missing required atttributes, skipping employee record (id={0})", employee.Id);
                        continue;
                    }

                    string badge = attrs.GetValueOrDefault("badge_no");
                    var requiredBadgeNo = employee.ClockSeq.TrimStart('0');

                    // skip all employees with a badge no, warn about some
                    if (!string.IsNullOrEmpty(badge))
                    {
                        if (badge != requiredBadgeNo)
                        {
                            Log.Logger.Error("Badge and clockseq mismatch for employee record (id={0}) cur:{1}, req:{2}",
                                employee.Id, badge, requiredBadgeNo);
                        }

                        continue;
                    }

                    employee.Attributes["badge_no"] = requiredBadgeNo;
                    currentEmployees.Add(employee);
                }

                ConsoleService.PrintFormattedJson(currentEmployees);
                return currentEmployees.Count;
            });

            return exitCode;
        }

        private int ProcessChangeResults(IEnumerable<Payroll.Shared.Result> results, bool dryRun)
        {
            // basically another safety measure
            ExecutionMode executionMode = Config.ExecutionMode();
            if (executionMode == ExecutionMode.Execute)
            {
                // both the config file and the command line must confirm execution
                if (dryRun) executionMode = ExecutionMode.DryRun;
            }

            Log.Logger.Information("Processing change results..");
            var dEntries = new List<EmployeeDirectoryEntry>();

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                ILiteCollection<Employee> empCache = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                foreach (var result in results)
                {
                    if (!result.Args.ContainsKey("pkey"))
                    {
                        Log.Logger.Error("Missing employee id in result");
                        continue;
                    }

                    var employeeId = result.Args["pkey"];
                    Log.Logger.Information("Processing result: {0}", employeeId);

                    var employee = empCache.FindById(employeeId);
                    if (employee == null)
                    {
                        Log.Logger.Error("Failed to find employee with id={0}", employeeId);
                        continue;
                    }

                    EmployeeDirectoryEntry dEntry = Converter.EmployeeToDirectoryEntry(employee, false);
                    Converter.AddPolicyAndGroupAttributesVersion2(dEntry);
                    dEntries.Add(dEntry);
                }
            }

            ConsoleService.PrintFormattedJson(dEntries);
            return 0;
        }

        public int Changes(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetResultsFromInput(out List<Result> results))
                {
                    Log.Logger.Error("Failed to parse results");
                    return ExitCode.BadInput;
                }

                if (results == null || results.Count == 0)
                {
                    Console.WriteLine("[]");
                    return ExitCode.Success;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
                ProcessChangeResults(results, dryRun);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        public int Test(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees hires list");
                    return ExitCode.BadInput;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return ExitCode.BadInput;
                }

                List<EmployeeDirectoryEntry> newEmployees = ValidNewHiresToDirectoryEntries(employees);
                Log.Logger.Information("Processing validated new employees..");

                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    ILiteCollection<Employee> empCache = cache.GetCollection<Employee>("employees");
                    RockhillIdGenerator idGenerator = new RockhillIdGenerator(empCache);

                    foreach (var dEntry in newEmployees)
                    {
                        uint nextId = idGenerator.GetNextAvailableId();
                        string nextIdToTry = nextId.ToString();

                        // set employeeId based on currently used numbers, yes this is ugly
                        dEntry.BadgeNo = nextIdToTry;
                        dEntry.EmployeeId = dEntry.BadgeNo.PadLeft(10, '0');

                        Converter.AddPolicyAndGroupAttributesVersion2(dEntry);
                    }
                }

                ConsoleService.PrintFormattedJson(newEmployees);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        public int Hires(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees hires list");
                    return ExitCode.BadInput;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return ExitCode.BadInput;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");
                ProcessNewHires(employees, dryRun);
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        private (int exitCode, int recordCount) ProcessImportCommand(List<string> args, Func<IEnumerable<Payroll.Shared.Employee>, int> func)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out List<Employee> employees))
                {
                    Log.Logger.Error("Failed to parse employee list");
                    return (ExitCode.BadInput, 0);
                }

                if (employees == null)
                {
                    Log.Logger.Error("Skipping null employees list");
                    return (ExitCode.BadInput, 0);
                }

                return (ExitCode.Success, func(employees));
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return (ExitCode.Exception, 0);
            }
        }

        private bool ValidTermination(Employee employee, bool only_immediate_terminations)
        {
            if (employee.Active)
            {
                Log.Logger.Error("Skipping active employee record (id={0})", employee.Id);
                return false;
            }

            if (string.IsNullOrEmpty(employee.ClockSeq))
            {
                Log.Logger.Error("Skipping new employee record (id={0})", employee.Id);
                return false;
            }

            if (employee.TermDate == null)
            {
                Log.Logger.Warning("Null term date, skipping employee record (id={0})", employee.Id);
                return false;
            }

            var now = DateTime.Now;
            if (employee.TermDate > DateTime.Now)
            {
                Log.Logger.Information("Future termination, skipping employee record (id={0}) with term date {1}", employee.Id, employee.TermDate);
                return false;
            }

            if (only_immediate_terminations && !Config.IsImmediateTermLabel(employee.Description))
            {
                Log.Logger.Debug("Not an immediate termination label, skipping employee record (id={0})", employee.Id);
                return false;
            }

            return true;
        }

        public void Terms(List<string> args)
        {
            var only_immediate_terminations = false;
            if (args != null & args.Count > 0)
            {
                if (args[0] == "immed_only") only_immediate_terminations = true;
            }

            ProcessImportCommand(args, (employees) =>
            {
                var currentEmployees = new List<EmployeeDirectoryEntry>();

                foreach (var employee in employees)
                {
                    if (!ValidTermination(employee, only_immediate_terminations))
                        continue;

                    Log.Logger.Information("Prepping termination of employee record (id={0})...", employee.Id);
                    var dEntry = Converter.EmployeeToDirectoryEntry(employee, false);

                    // Rockhill wants to move these users when terminated
                    dEntry.ParentOU = "OU=NeverNever,DC=CityofRockHillSC,DC=gov";
                    var comment = $"{dEntry.GivenName} {dEntry.SurName} terminated {employee.TermDate?.ToShortDateString()}. Info: {dEntry.Department}, {dEntry.DivisionName}, {dEntry.DivisionCode}, {dEntry.Title}";

                    // Save these fields in the description
                    dEntry.Description = comment;
                    currentEmployees.Add(dEntry);
                }

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Converters = { new EmployeeTermConverter() }
                };

                ConsoleService.PrintFormattedJson(currentEmployees);
                return currentEmployees.Count;

            });
        }

        public bool SetNewBadgeAndEmployeeId(RockhillIdGenerator idGenerator, EmployeeDirectoryEntry dEntry)
        {
            if (string.IsNullOrEmpty(dEntry.ExternalId))
            {
                Log.Logger.Error("No SSN, cannot verify employee id security restrictions, skipping {0} {1}", dEntry.GivenName, dEntry.SurName);
                return false;
            }

            var passedSsnTest = false;
            string nextIdToTry = "";

            while (!passedSsnTest)
            {
                var fndSnippet = false;

                // consume this id
                uint nextId = idGenerator.GetNextAvailableId();
                nextIdToTry = nextId.ToString();

                // we test with the padding because BOTH the badgeno and clockseq must pass this SSN filter, so we kill 2 birds with 1 stone here
                var testTheNextIdWithPadding = nextIdToTry.PadLeft(10, '0');

                for (var i = 0; i < dEntry.ExternalId.Length - 3; i++)
                {
                    var snippet = dEntry.ExternalId.Substring(i, 3);
                    if (testTheNextIdWithPadding.Contains(snippet))
                    {
                        fndSnippet = true;
                        break;
                    }
                }

                if (!fndSnippet) passedSsnTest = true;
            }

            // set employeeId based on currently used numbers, yes this is ugly
            dEntry.BadgeNo = nextIdToTry;
            dEntry.EmployeeId = dEntry.BadgeNo.PadLeft(10, '0');

            // external id is SSN. need to set here as this is the first time for a new hire that we have their badge number
            if (!string.IsNullOrEmpty(dEntry.ExternalId))
                dEntry.EmployeeNumber = dEntry.BadgeNo + dEntry.ExternalId.Tail(2);

            return true;
        }

        private int ProcessNewHires(IEnumerable<Payroll.Shared.Employee> employees, bool dryRun)
        {
            // basically another safety measure
            ExecutionMode executionMode = Config.ExecutionMode();
            if (executionMode == ExecutionMode.Execute)
            {
                // both the config file and the command line must confirm execution
                if (dryRun) executionMode = ExecutionMode.DryRun;
            }

            List<EmployeeDirectoryEntry> newEmployees = ValidNewHiresToDirectoryEntries(employees);
            Log.Logger.Information("Processing validated new employees..");

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                ILiteCollection<Employee> empCache = cache.GetCollection<Employee>("employees");
                RockhillIdGenerator idGenerator = new RockhillIdGenerator(empCache);

                foreach (var dEntry in newEmployees)
                {
                    if (string.IsNullOrEmpty(dEntry.EmployeeId))
                    {
                        // this might fail, so must abort if so...
                        if (!SetNewBadgeAndEmployeeId(idGenerator, dEntry)) continue;
                    }
                    else
                    {
                        // warn about HR jumping the gun on us
                        Log.Logger.Warning("New employee with pre-existing clockseq and badge, not allocating a new rockill id programatically ({0}, {1})", dEntry.BadgeNo, dEntry.SurName);
                    }

                    Converter.AddPolicyAndGroupAttributesVersion2(dEntry);
                }
            }

            ConsoleService.PrintFormattedJson(newEmployees);
            return newEmployees.Count;
        }

    }
}