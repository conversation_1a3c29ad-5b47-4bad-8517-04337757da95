﻿using Payroll.Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;

namespace Paycom2.Tool
{
    public enum BreakModeOption
    {
        Original,
        Lunches,
        Dynamic
    };

    public enum SSNMode
    {
        LastTwoDigits,
        Full
    }

    public static class Config
    {
        public readonly static string SettingSection = "paycom";
        public readonly static string EMPLOYEES_CACHE_COLLECTION = "employees";

        public readonly static string PunchImportFrequency = "unspecified";
        public readonly static bool IncludePending = false;
        public readonly static bool IncludeRatesbyAllocation = false; // defaults to false for backwards compatibility (rockhill)
        public readonly static bool IncludeSensitive = true; // defaults to true for backwards compatibility (rockhill)

        public static Dictionary<string, string> CustomFields { get; private set; }
        public static Dictionary<string, string> TimeZoneAdjustments { get; private set; }

        static Config()
        {
            Setting.Init();

            CustomFields = Setting.ListSection("paycom_custom_fields");
            TimeZoneAdjustments = Setting.ListSection("paycom_timeadj");

            var sensitive = Setting.Get(SettingSection, "sensitive").Trim();
            if (!string.IsNullOrEmpty(value: sensitive))
                IncludeSensitive = (sensitive == "1");

            var rates = Setting.Get(SettingSection, "rates").Trim();
            if (!string.IsNullOrEmpty(value: rates))
                IncludeRatesbyAllocation = (rates == "1");

            // consider pending employees as active?
            var pending = Setting.Get(SettingSection, "pending").Trim();
            if (!string.IsNullOrEmpty(pending))
                IncludePending = (pending == "1");

            if (CustomFields == null)
                CustomFields = new Dictionary<string, string>();

            if (TimeZoneAdjustments == null)
                TimeZoneAdjustments = new Dictionary<string, string>();

            var punchImportFreq = Setting.Get(SettingSection, "punch_import_frequency").Trim();
            if (!string.IsNullOrEmpty(punchImportFreq))
                PunchImportFrequency = punchImportFreq;
        }

        public static ExecutionMode ExecutionMode()
        {
            return Setting.ExecutionMode(SettingSection, "mode");
        }

        public static bool NotSupportedChangeType(string changeType)
        {
            var NotSupportedChangeTypes = new List<string>
            {
                "Net Pay Bank Name",
                "System Event: EVENT_ONEESS",
                "Accrual [Floating Holiday] Current Year Awarded",
                "Ethnicity",
                "Shirt Size",
                "Cobra Initial Notice",
                "AD&D (amount)",
                "[EE_TAXES] Exempt Federal Taxes",
                "PAF: Year-End Tax Forms Election",
                "Enable Direct Deposit for Distributions",
                "[EE_TAXES] Federal Tax - Additional Amount",
                "Dist or Net first"
            };

            return NotSupportedChangeTypes.Contains(changeType);
        }

        public static string TimeZone()
        {
            var setting = Setting.Get(SettingSection, "timezone");
            if (!string.IsNullOrEmpty(setting)) return setting;
            var ptz = Environment.GetEnvironmentVariable("PAYCOM_TIMEZONE");
            return ptz ?? "UTC";
        }

        public static int TimeZoneAdjust(string timeZone)
        {
            if (string.IsNullOrEmpty(timeZone))
                return 0;

            if (TimeZoneAdjustments.TryGetValue(timeZone, out var adjust))
                return Convert.ToInt32(adjust);
            return 0;
        }

        public static string PaycomToken()
        {
            var setting = Setting.Get(SettingSection, "token");
            if (!string.IsNullOrEmpty(setting)) return setting;
            return Environment.GetEnvironmentVariable("PAYCOM_TOKEN");
        }

        public static string PaycomSid()
        {
            var setting = Setting.Get(SettingSection, "sid");
            if (!string.IsNullOrEmpty(setting)) return setting;
            return Environment.GetEnvironmentVariable("PAYCOM_SID");
        }

        public static string PaycomLocation()
        {
            var setting = Setting.Get(SettingSection, "location");
            if (!string.IsNullOrEmpty(setting)) return setting;
            return Environment.GetEnvironmentVariable("PAYCOM_LOCATION");
        }

        public static DateTime? GetLastPunchImportRunTime(string importType = "default")
        {
            return CacheService.GetLastRunTime($"PaycomPunches_{importType}");
        }

        public static void SetLastPunchImportRunTime(DateTime dateTime, string importType = "default")
        {
            CacheService.CacheLastRunTime($"PaycomPunches_{importType}", dateTime);
        }

        public static bool IsPunchImportTooSoon(DateTimeOffset now, string importType = "default")
        {
            if (PunchImportFrequency == "unspecified") return false;

            var lastRun = GetLastPunchImportRunTime(importType);
            if (lastRun == null) return false;

            if (PunchImportFrequency == "daily")
            {
                if (now.Date == lastRun.Value.Date) return true;
            }
            else if (PunchImportFrequency == "weekly")
            {
                if (ISOWeek.GetWeekOfYear(now.Date) == ISOWeek.GetWeekOfYear(lastRun.Value.Date)) return true;
            }
            else if (PunchImportFrequency == "biweekly")
            {
                var week = ISOWeek.GetWeekOfYear(now.Date);
                if (week % 2 == ISOWeek.GetWeekOfYear(lastRun.Value.Date) % 2) return true;
            }
            else if (PunchImportFrequency == "every14days")
            {
                // Calculate the time span between now and the last run
                var daysSinceLastRun = (now.Date - lastRun.Value.Date).TotalDays;
                // If less than 14 days have passed, it's too soon
                if (daysSinceLastRun < 14) return true;
            }

            return false;
        }

        public static bool HasPrimaryJob()
        {
            var setting = Setting.Get(SettingSection, "primary_job");
            if (string.IsNullOrEmpty(setting)) return false;
            return setting == "1";
        }

        public static BreakModeOption BreakMode()
        {
            var setting = Setting.Get(SettingSection, "breakmode");
            return setting switch
            {
                "original" => BreakModeOption.Original,
                "dynamic" => BreakModeOption.Dynamic,
                "lunches" => BreakModeOption.Lunches,
                _ => BreakModeOption.Original
            };
        }

        public static SSNMode SocialSecurityNumberMode()
        {
            var setting = Setting.Get(SettingSection, "ssn_mode");
            return setting switch
            {
                "last_2" => SSNMode.LastTwoDigits,
                "full" => SSNMode.Full,
                _ => SSNMode.LastTwoDigits
            };
        }

        public static string StartBreakCode(BreakModeOption breakMode, Break brk)
        {
            return breakMode switch
            {
                BreakModeOption.Original => "OB",
                BreakModeOption.Lunches => "OL",
                BreakModeOption.Dynamic => !brk.Paid ? "OL" : "OB",
                _ => "OB"
            };
        }

        public static string EndBreakCode(BreakModeOption breakMode, Break brk)
        {
            return breakMode switch
            {
                BreakModeOption.Original => "IB",
                BreakModeOption.Lunches => "IL",
                BreakModeOption.Dynamic => !brk.Paid ? "IL" : "IB",
                _ => "IB"
            };
        }
    }
}
